# import asyncio
# import csv
# import html
# import re
# from typing import List, Set, Tuple

# from playwright.async_api import async_playwright, Page

# # eBay Trending Deals page for US
# TRENDING_URL = "https://www.ebay.com/deals/trending/all"
# TARGET_TOTAL = 100  # This value is no longer needed for saving the full list
# EBAY_ITEMID = re.compile(r"/itm/(?:.*?/)?(?P<id>\d{9,12})(?:[/?#]|$)", re.I)

# # ---------------- Utilities ----------------

# def clean_title(s: str) -> str:
#     s = html.unescape((s or "").strip().strip('"'))
#     s = re.sub(r"\s+", " ", s)
#     for splitter in (" | ", " – ", " — ", " - "):
#         if splitter in s:
#             s = s.split(splitter, 1)[0].strip()
#     s = re.split(r":\s+", s, 1)[0]
#     s = re.sub(r"\s*\([^()]{0,40}\)\s*$", "", s).strip()
#     return s.strip().strip('"')

# def is_noise_title(t: str) -> bool:
#     if not t:
#         return True
#     tl = t.lower()
#     return any(x in tl for x in [
#         "new listing", "shop on ebay", "sponsored",
#         "see details", "see price", "more like this",
#         "see all trending deals",
#     ])

# async def count_visible_items(page: Page) -> int:
#     ids: Set[str] = set()
#     # Look for product ID in URL (inside a <a> tag)
#     for a in await page.locator("a[href*='/itm/']").all():
#         href = (await a.get_attribute("href")) or ""
#         m = EBAY_ITEMID.search(href)
#         if m:
#             ids.add(m.group(1))  # Unique product ID
#     return len(ids)

# # ---------------- Loader: click “Show more” until 100 ----------------

# async def load_all_items(page: Page, target: int = 100) -> None:
#     """
#     Clicks the 'Show more' button repeatedly until at least `target` items are visible,
#     or until no further increase in visible products is seen.
#     """
#     last_count = -1
#     stagnant_rounds = 0

#     while True:
#         cur = await count_visible_items(page)
#         print(f"Currently loaded: {cur}")
#         if cur >= target:
#             break

#         # Click the 'Show more' button
#         btn = page.locator("button:has-text('Show more')")
#         if await btn.count() == 0:
#             # Fallback to other button variants
#             btn = page.locator(
#                 "[role='button']:has-text('Show more'), a:has-text('Show more')"
#             )

#         if await btn.count() > 0:
#             try:
#                 b = btn.first
#                 await b.scroll_into_view_if_needed()
#                 await b.wait_for(state="visible", timeout=5000)
#                 print("Clicking 'Show more'…")
#                 await b.click()
#                 # Wait for new tiles to load
#                 await page.wait_for_timeout(2000)
#             except Exception as e:
#                 print(f"⚠️ Could not click 'Show more': {e}")
#                 break
#         else:
#             print("No 'Show more' button found, stopping.")
#             break

#         new_count = await count_visible_items(page)
#         if new_count <= last_count:
#             stagnant_rounds += 1
#         else:
#             stagnant_rounds = 0
#         last_count = new_count
#         if stagnant_rounds >= 3:
#             print("No further growth after clicks, stopping.")
#             break

# # ---------------- Extraction ----------------

# async def extract_titles(page: Page) -> List[Tuple[str, str]]:
#     """
#     Returns (item_id, title) pairs from visible tiles on the page.
#     """
#     results: List[Tuple[str, str]] = []

#     tile_selectors = [
#         "div.card__content",
#         "div.card__details",  # For main content block in eBay tiles
#         "[data-testid*='product-card']",
#     ]
#     tiles = []
#     for sel in tile_selectors:
#         tiles.extend(await page.locator(sel).all())
#     if not tiles:
#         tiles = await page.locator("a[href*='/itm/']").all()

#     for t in tiles:
#         a = t.locator("a[href*='/itm/']")
#         if await a.count() == 0:
#             continue
#         a = a.first
#         href = (await a.get_attribute("href")) or ""
#         m = EBAY_ITEMID.search(href)
#         if not m:
#             continue
#         item_id = m.group(1)

#         # Title candidates (robust order)
#         title = ""
#         title_selectors = [
#             ".product-card-title", ".card-title", 
#             "[data-testid*='product-name']", "h2, h3, h4"
#         ]
#         for sel in title_selectors:
#             loc = t.locator(sel)
#             if await loc.count() > 0:
#                 txt = ((await loc.first.inner_text()) or "").strip()
#                 if txt and not is_noise_title(txt):
#                     title = txt
#                     break
#         if not title:
#             title = (await a.get_attribute("aria-label")) or ""
#         if (not title) or is_noise_title(title):
#             title = (await a.get_attribute("title")) or title
#         if (not title) or is_noise_title(title):
#             img = t.locator("img[alt]")
#             if await img.count() > 0:
#                 title = (await img.first.get_attribute("alt")) or title
#         if (not title) or is_noise_title(title):
#             txt = ((await a.inner_text()) or "").strip()
#             if txt and not is_noise_title(txt):
#                 title = txt

#         title = clean_title(title)
#         if not title or is_noise_title(title):
#             continue

#         results.append((item_id, title))

#     # Safety net: sweep anchors we might have missed
#     anchors = await page.locator("a[href*='/itm/']").all()
#     for a in anchors:
#         href = (await a.get_attribute("href")) or ""
#         m = EBAY_ITEMID.search(href)
#         if not m:
#             continue
#         item_id = m.group(1)
#         title = (await a.get_attribute("aria-label")) or ""
#         if not title:
#             img = a.locator("img[alt]")
#             if await img.count() > 0:
#                 title = (await img.first.get_attribute("alt")) or ""
#         if not title:
#             title = ((await a.inner_text()) or "").strip()
#         title = clean_title(title)
#         if not title or is_noise_title(title):
#             continue
#         results.append((item_id, title))

#     return results

# # ---------------- Main ----------------

# async def main():
#     async with async_playwright() as pw:
#         browser = await pw.chromium.launch(headless=False)
#         ctx = await browser.new_context(
#             locale="en-US",
#             user_agent=("Mozilla/5.0 (Windows NT 10.0; Win64; x64) "
#                         "AppleWebKit/537.36 (KHTML, like Gecko) "
#                         "Chrome/********* Safari/537.36"),
#             viewport={"width": 1366, "height": 900},
#         )
#         page = await ctx.new_page()

#         print("Opening eBay Trending …")
#         await page.goto(TRENDING_URL, wait_until="domcontentloaded", timeout=60000)
#         await page.wait_for_timeout(1200)

#         # Ensure all items load by clicking "Show more"
#         await load_all_items(page, target=TARGET_TOTAL)

#         pairs = await extract_titles(page)

#         # Dedupe by item id, then by normalized text; keep all unique titles
#         seen_ids: Set[str] = set()
#         seen_text: Set[str] = set()
#         titles: List[str] = []
#         for item_id, title in pairs:
#             if item_id in seen_ids:
#                 continue
#             key = title.lower()
#             if key in seen_text:
#                 continue
#             seen_ids.add(item_id)
#             seen_text.add(key)
#             titles.append(title)

#         # Save all titles (no limit on the number saved)
#         out = "ebay_trending_all_products.csv"
#         with open(out, "w", newline="", encoding="utf-8") as f:
#             w = csv.writer(f)
#             w.writerow(["Product Title"])
#             for t in titles:
#                 w.writerow([t])

#         await ctx.close()
#         await browser.close()
#         print(f"✅ Saved {len(titles)} titles to {out}")

# if __name__ == "__main__":
#     asyncio.run(main())

import asyncio
import csv
import html
import re
from typing import List, Set, Tuple

from playwright.async_api import async_playwright, Page

# eBay Trending Deals page for US
TRENDING_URL = "https://www.ebay.com/deals/trending/all"
TARGET_TOTAL = 100
EBAY_ITEMID = re.compile(r"/itm/(?:.*?/)?(?P<id>\d{9,12})(?:[/?#]|$)", re.I)

# ---------------- Utilities ----------------

def clean_title(s: str) -> str:
    s = html.unescape((s or "").strip().strip('"'))
    s = re.sub(r"\s+", " ", s)
    for splitter in (" | ", " – ", " — ", " - "):
        if splitter in s:
            s = s.split(splitter, 1)[0].strip()
    s = re.split(r":\s+", s, 1)[0]
    s = re.sub(r"\s*\([^()]{0,40}\)\s*$", "", s).strip()
    return s.strip().strip('"')

def is_noise_title(t: str) -> bool:
    if not t:
        return True
    tl = t.lower()
    return any(x in tl for x in [
        "new listing", "shop on ebay", "sponsored",
        "see details", "see price", "more like this",
        "see all trending deals",
    ])

async def count_visible_items(page: Page) -> int:
    ids: Set[str] = set()
    for a in await page.locator("a[href*='/itm/']").all():
        href = (await a.get_attribute("href")) or ""
        m = EBAY_ITEMID.search(href)
        if m:
            ids.add(m.group(1))
    return len(ids)

# ---------------- Loader ----------------

async def load_all_items(page: Page, target: int = 100) -> None:
    last_count = -1
    stagnant_rounds = 0

    while True:
        cur = await count_visible_items(page)
        print(f"Currently loaded: {cur}")
        if cur >= target:
            break

        btn = page.locator("button:has-text('Show more')")
        if await btn.count() == 0:
            btn = page.locator("[role='button']:has-text('Show more'), a:has-text('Show more')")

        if await btn.count() > 0:
            try:
                b = btn.first
                await b.scroll_into_view_if_needed()
                await b.wait_for(state="visible", timeout=5000)
                print("Clicking 'Show more'…")
                await b.click()
                await page.wait_for_timeout(2000)
            except Exception as e:
                print(f"⚠️ Could not click 'Show more': {e}")
                break
        else:
            print("No 'Show more' button found, stopping.")
            break

        new_count = await count_visible_items(page)
        if new_count <= last_count:
            stagnant_rounds += 1
        else:
            stagnant_rounds = 0
        last_count = new_count
        if stagnant_rounds >= 3:
            print("No further growth after clicks, stopping.")
            break

# ---------------- Category Select ----------------

async def click_all_categories(page: Page):
    # Open dropdown if needed
    btn = page.locator("#category-dropdown")
    if await btn.count() > 0:
        expanded = (await btn.first.get_attribute("aria-expanded")) or "false"
        if expanded == "false":
            await btn.first.click()
            await page.wait_for_timeout(800)

    # Look for "All Categories" link
    all_link = page.locator("a[title='All Categories'][href='https://www.ebay.com/deals/trending/all']")
    if await all_link.count() > 0:
        async with page.expect_navigation(wait_until="domcontentloaded", timeout=15000):
            await all_link.first.click()
        return

    # Fallback: navigate directly
    if "/deals/trending/all" not in page.url:
        await page.goto("https://www.ebay.com/deals/trending/all", wait_until="domcontentloaded")

# ---------------- Extraction ----------------

async def extract_titles(page: Page) -> List[Tuple[str, str]]:
    results: List[Tuple[str, str]] = []

    tile_selectors = [
        "div.card__content",
        "div.card__details",
        "[data-testid*='product-card']",
    ]
    tiles = []
    for sel in tile_selectors:
        tiles.extend(await page.locator(sel).all())
    if not tiles:
        tiles = await page.locator("a[href*='/itm/']").all()

    for t in tiles:
        a = t.locator("a[href*='/itm/']")
        if await a.count() == 0:
            continue
        a = a.first
        href = (await a.get_attribute("href")) or ""
        m = EBAY_ITEMID.search(href)
        if not m:
            continue
        item_id = m.group(1)

        title = ""
        for sel in [".product-card-title", ".card-title", "[data-testid*='product-name']", "h2, h3, h4"]:
            loc = t.locator(sel)
            if await loc.count() > 0:
                txt = ((await loc.first.inner_text()) or "").strip()
                if txt and not is_noise_title(txt):
                    title = txt
                    break
        if not title:
            title = (await a.get_attribute("aria-label")) or ""
        if (not title) or is_noise_title(title):
            title = (await a.get_attribute("title")) or title
        if (not title) or is_noise_title(title):
            img = t.locator("img[alt]")
            if await img.count() > 0:
                title = (await img.first.get_attribute("alt")) or title
        if (not title) or is_noise_title(title):
            txt = ((await a.inner_text()) or "").strip()
            if txt and not is_noise_title(txt):
                title = txt

        title = clean_title(title)
        if not title or is_noise_title(title):
            continue

        results.append((item_id, title))

    # Sweep anchors missed
    anchors = await page.locator("a[href*='/itm/']").all()
    for a in anchors:
        href = (await a.get_attribute("href")) or ""
        m = EBAY_ITEMID.search(href)
        if not m:
            continue
        item_id = m.group(1)
        title = (await a.get_attribute("aria-label")) or ""
        if not title:
            img = a.locator("img[alt]")
            if await img.count() > 0:
                title = (await img.first.get_attribute("alt")) or ""
        if not title:
            title = ((await a.inner_text()) or "").strip()
        title = clean_title(title)
        if not title or is_noise_title(title):
            continue
        results.append((item_id, title))

    return results

# ---------------- Main ----------------

async def main():
    async with async_playwright() as pw:
        browser = await pw.chromium.launch(headless=False)
        ctx = await browser.new_context(
            locale="en-US",
            user_agent=("Mozilla/5.0 (Windows NT 10.0; Win64; x64) "
                        "AppleWebKit/537.36 (KHTML, like Gecko) "
                        "Chrome/********* Safari/537.36"),
            viewport={"width": 1366, "height": 900},
        )
        page = await ctx.new_page()

        print("Opening eBay Trending …")
        await page.goto(TRENDING_URL, wait_until="domcontentloaded", timeout=60000)
        await page.wait_for_timeout(1200)

        # NEW: Make sure "All Categories" is clicked
        await click_all_categories(page)

        await load_all_items(page, target=TARGET_TOTAL)
        pairs = await extract_titles(page)

        seen_ids: Set[str] = set()
        seen_text: Set[str] = set()
        titles: List[str] = []
        for item_id, title in pairs:
            if item_id in seen_ids:
                continue
            key = title.lower()
            if key in seen_text:
                continue
            seen_ids.add(item_id)
            seen_text.add(key)
            titles.append(title)

        out = "ebay_trending_all_products.csv"
        with open(out, "w", newline="", encoding="utf-8") as f:
            w = csv.writer(f)
            w.writerow(["Product Title"])
            for t in titles:
                w.writerow([t])

        await ctx.close()
        await browser.close()
        print(f"✅ Saved {len(titles)} titles to {out}")

if __name__ == "__main__":
    asyncio.run(main())




