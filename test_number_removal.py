#!/usr/bin/env python3
"""
Test script to verify that unwanted numbers at the beginning of generic names are removed
"""

import re
from AmazonScrapGenric import _clean_model_text

def test_number_removal():
    """Test various cases of unwanted numbers at the beginning"""
    
    test_cases = [
        # (input, expected_output)
        ("1. Apple iPhone 14 Pro", "Apple iPhone 14 Pro"),
        ("2) Samsung Galaxy S23", "Samsung Galaxy S23"),
        ("3- Sony WH-1000XM4", "Sony WH-1000XM4"),
        ("4 Dell XPS 13", "Dell XPS 13"),
        ("10. Titleist Pro V1", "Titleist Pro V1"),
        ("Apple iPhone 14 Pro", "Apple iPhone 14 Pro"),  # No number - should stay same
        ("Samsung 55 QLED TV", "Samsung 55 QLED TV"),  # Number in middle - should stay
        ("1.2.3 Apple iPhone", "Apple iPhone"),  # Multiple numbers/dots
        ("123) Samsung Galaxy", "Samsung Galaxy"),  # Multiple digits
        ("- Apple iPhone", "Apple iPhone"),  # Just dash
        (". Sony Headphones", "Sony Headphones"),  # Just dot
        ("Output: Apple iPhone", "Apple iPhone"),  # Common prefix
        ("Generic: 1. Samsung TV", "Samsung TV"),  # Both prefix and number
    ]
    
    print("=== Testing Number Removal from Generic Names ===")
    print()
    
    all_passed = True
    
    for i, (input_text, expected) in enumerate(test_cases, 1):
        result = _clean_model_text(input_text)
        passed = result == expected
        all_passed = all_passed and passed
        
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"Test {i:2d}: {status}")
        print(f"  Input:    '{input_text}'")
        print(f"  Expected: '{expected}'")
        print(f"  Got:      '{result}'")
        if not passed:
            print(f"  ❌ MISMATCH!")
        print()
    
    return all_passed

def test_with_sample_products():
    """Test with actual product data that might have numbers"""
    
    print("=== Testing with Sample Product Titles ===")
    print()
    
    sample_titles = [
        "1. Apple iPhone 14 Pro Max (256GB) - Deep Purple",
        "2) Samsung Galaxy S23 Ultra 5G (512GB) - Phantom Black", 
        "3- Sony WH-1000XM4 Wireless Noise Canceling Headphones - Black",
        "10. Dell XPS 13 Plus 9320 Laptop - Platinum Silver",
        "Apple AirPods Pro (2nd Generation) with MagSafe Case - White",  # No number
    ]
    
    for i, title in enumerate(sample_titles, 1):
        cleaned = _clean_model_text(title)
        print(f"Product {i}:")
        print(f"  Original: '{title}'")
        print(f"  Cleaned:  '{cleaned}'")
        print()

if __name__ == "__main__":
    print("Testing unwanted number removal from generic names...")
    print("=" * 70)
    
    # Test basic number removal
    success = test_number_removal()
    
    # Test with sample products
    test_with_sample_products()
    
    if success:
        print("🎉 All tests passed! Numbers at the beginning will be removed.")
    else:
        print("❌ Some tests failed. Check the output above.")
    
    print("\nThe following patterns will be removed from the beginning:")
    print("- '1. ' -> removed")
    print("- '2) ' -> removed") 
    print("- '3- ' -> removed")
    print("- '10. ' -> removed")
    print("- 'Output: ' -> removed")
    print("- 'Generic: ' -> removed")
    print("\nBut numbers in the middle/end are kept:")
    print("- 'Samsung 55 QLED' -> kept")
    print("- 'iPhone 14 Pro' -> kept")
