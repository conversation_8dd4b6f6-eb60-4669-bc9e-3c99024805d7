#!/usr/bin/env python3
"""
Demo script to test the combined scraper with sample data
"""

import asyncio
import os
from combined_scraper import build_generic_csv_from_rows, save_raw_csv

def demo_combined_scraper():
    """Demo the combined scraper functionality with sample data."""
    print("🚀 Combined Scraper Demo")
    print("=" * 50)
    
    # Sample products from different platforms
    sample_products = [
        # Amazon products
        {
            "name": "Apple iPhone 14 Pro Max (256GB) - Deep Purple",
            "category": "Electronics",
            "href": "https://amazon.com/dp/B0BDJB9NCF",
            "domain": "amazon.com",
            "source": "amazon"
        },
        {
            "name": "Samsung Galaxy S23 Ultra 5G (512GB) - Phantom Black",
            "category": "Electronics", 
            "href": "https://amazon.com/dp/B0BLP4JQZV",
            "domain": "amazon.com",
            "source": "amazon"
        },
        {
            "name": "Sony WH-1000XM4 Wireless Noise Canceling Headphones - Black",
            "category": "Electronics",
            "href": "https://amazon.com/dp/B0863TXGM3",
            "domain": "amazon.com", 
            "source": "amazon"
        },
        # eBay products
        {
            "name": "1. Apple AirPods Pro (2nd Generation) with MagSafe Case - White",
            "category": "Electronics",
            "href": "https://ebay.com/itm/123456789",
            "domain": "ebay.com",
            "source": "ebay"
        },
        {
            "name": "2) Ninja Foodi Personal Blender with 18 oz BPA-Free Cup - Black/Silver (2-Pack)",
            "category": "Home",
            "href": "https://ebay.com/itm/987654321",
            "domain": "ebay.com",
            "source": "ebay"
        },
        # SERP products
        {
            "name": "Dell XPS 13 Plus 9320 Laptop, 13.4\" OLED Touch, Core i7-1360P, 32GB RAM, 1TB SSD - Platinum Silver",
            "category": "Technology",
            "href": "https://example.com/dell-xps",
            "domain": "example.com",
            "source": "serp"
        },
        {
            "name": "3- Titleist Pro V1 Golf Balls (One Dozen) - White",
            "category": "Sports",
            "href": "https://golfstore.com/titleist",
            "domain": "golfstore.com",
            "source": "serp"
        },
        # Duplicate for testing scoring
        {
            "name": "Apple iPhone 14 Pro Max (256GB) - Space Black",
            "category": "Electronics",
            "href": "https://bestbuy.com/iphone14",
            "domain": "bestbuy.com",
            "source": "serp"
        }
    ]
    
    print(f"📦 Processing {len(sample_products)} sample products...")
    
    # Save raw CSV
    raw_csv = "demo_combined_products.csv"
    save_raw_csv(raw_csv, sample_products)
    
    # Generate generic names
    print("\n🤖 Generating AI-powered generic names...")
    build_generic_csv_from_rows(raw_csv, sample_products)
    
    # Display results
    generic_csv = "demo_combined_products.generic.csv"
    if os.path.exists(generic_csv):
        import pandas as pd
        df = pd.read_csv(generic_csv)
        
        print(f"\n📊 Results Summary:")
        print(f"Total products: {len(df)}")
        print(f"Unique generic names: {df['generic_name'].nunique()}")
        print(f"Domains: {', '.join(df['domain'].unique())}")
        
        print(f"\n📋 Generated Generic Names:")
        print("=" * 80)
        for idx, row in df.iterrows():
            print(f"{idx+1:2d}. '{row['generic_name']}'")
            print(f"    Domain: {row['domain']} | Score: {row['score']}")
            print("-" * 60)
        
        print(f"\n✅ Demo complete!")
        print(f"📁 Raw data: {raw_csv}")
        print(f"🤖 Generic names: {generic_csv}")
        
        # Show score distribution
        score_counts = df['score'].value_counts().sort_index()
        print(f"\n📈 Score Distribution:")
        for score, count in score_counts.items():
            print(f"  Score {score}: {count} products")
    
    else:
        print("❌ Generic CSV not created. Check Ollama connection.")

def show_usage_examples():
    """Show usage examples for the combined scraper."""
    print("\n" + "=" * 60)
    print("🔧 Combined Scraper Usage Examples")
    print("=" * 60)
    
    examples = [
        {
            "description": "Scrape Amazon Best Sellers only",
            "command": "python combined_scraper.py --platforms amazon --out amazon_products.csv --headless"
        },
        {
            "description": "Scrape both Amazon and eBay",
            "command": "python combined_scraper.py --platforms amazon ebay --out multi_platform.csv --headless"
        },
        {
            "description": "Scrape Google SERP with custom query",
            "command": "python combined_scraper.py --platforms serp --query 'trending gadgets 2024' --out serp_products.csv"
        },
        {
            "description": "Scrape all platforms with multiple queries",
            "command": "python combined_scraper.py --platforms amazon ebay serp --query 'viral products' 'trending tech' --out all_products.csv --headless"
        },
        {
            "description": "Skip generic name generation (raw data only)",
            "command": "python combined_scraper.py --platforms amazon --out raw_only.csv --no-generic --headless"
        }
    ]
    
    for i, example in enumerate(examples, 1):
        print(f"\n{i}. {example['description']}:")
        print(f"   {example['command']}")
    
    print(f"\n📝 Notes:")
    print(f"   • --headless: Run browsers without GUI (recommended for automation)")
    print(f"   • --no-generic: Skip AI-powered generic name generation")
    print(f"   • Generic names are saved to <filename>.generic.csv")
    print(f"   • Requires Ollama running with llama3.2:1b model for generic names")

if __name__ == "__main__":
    try:
        demo_combined_scraper()
        show_usage_examples()
    except KeyboardInterrupt:
        print("\n⚠️ Demo interrupted by user.")
    except Exception as e:
        print(f"\n❌ Demo error: {e}")
        import traceback
        traceback.print_exc()
