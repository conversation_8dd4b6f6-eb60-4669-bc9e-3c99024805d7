#!/usr/bin/env python3
"""
Test script for the combined scraper to verify functionality
"""

import os
import asyncio
import pandas as pd
from combined_scraper import (
    check_ollama_connection, 
    generate_generic_name,
    build_generic_csv_from_rows,
    get_available_models
)

def test_ollama_integration():
    """Test Ollama connection and generic name generation."""
    print("=== Testing Ollama Integration ===")
    
    # Test connection
    if not check_ollama_connection():
        print("❌ Ollama not accessible. Make sure Ollama is running.")
        return False
    
    print("✅ Ollama is accessible")
    
    # Test available models
    models = get_available_models()
    print(f"Available models: {models}")
    
    # Test generic name generation
    test_titles = [
        "Apple iPhone 14 Pro Max (256GB) - Deep Purple",
        "Samsung Galaxy S23 Ultra 5G (512GB) - Phantom Black",
        "Sony WH-1000XM4 Wireless Noise Canceling Headphones - Black",
        "1. Dell XPS 13 Plus 9320 Laptop - Platinum Silver",
        "2) Nike Air Max 270 Men's Running Shoes - Size 10"
    ]
    
    print("\nTesting generic name generation:")
    print("=" * 60)
    
    for title in test_titles:
        generic = generate_generic_name(title)
        print(f"Original: {title}")
        print(f"Generic:  {generic}")
        print("-" * 40)
    
    return True

def test_csv_generation():
    """Test CSV generation with sample data."""
    print("\n=== Testing CSV Generation ===")
    
    sample_products = [
        {
            "name": "Apple iPhone 14 Pro Max (256GB) - Deep Purple",
            "category": "Electronics",
            "href": "https://amazon.com/dp/B0BDJB9NCF",
            "domain": "amazon.com",
            "source": "amazon"
        },
        {
            "name": "Samsung Galaxy S23 Ultra 5G (512GB) - Phantom Black",
            "category": "Electronics", 
            "href": "https://amazon.com/dp/B0BLP4JQZV",
            "domain": "amazon.com",
            "source": "amazon"
        },
        {
            "name": "Sony WH-1000XM4 Wireless Noise Canceling Headphones - Black",
            "category": "Electronics",
            "href": "https://ebay.com/itm/123456789",
            "domain": "ebay.com", 
            "source": "ebay"
        },
        {
            "name": "1. Ninja Air Fryer AF101 - Black",
            "category": "Home",
            "href": "https://example.com/ninja-fryer",
            "domain": "example.com",
            "source": "serp"
        }
    ]
    
    test_csv = "test_combined_output.csv"
    
    try:
        # Test CSV generation
        build_generic_csv_from_rows(test_csv, sample_products)
        
        # Check if generic CSV was created
        generic_csv = "test_combined_output.generic.csv"
        if os.path.exists(generic_csv):
            print(f"✅ Generic CSV created: {generic_csv}")
            
            # Read and display the CSV structure
            df = pd.read_csv(generic_csv)
            print(f"\n📊 CSV Structure:")
            print(f"Columns: {list(df.columns)}")
            print(f"Shape: {df.shape}")
            
            print(f"\n📋 Sample Data:")
            print("=" * 80)
            for idx, row in df.iterrows():
                print(f"{idx+1:2d}. Generic Name: '{row['generic_name']}'")
                print(f"    Domain: '{row['domain']}'")
                print(f"    Score: {row['score']}")
                print("-" * 60)
            
            return True
        else:
            print(f"❌ Generic CSV not created: {generic_csv}")
            return False
            
    except Exception as e:
        print(f"❌ Error during CSV generation: {e}")
        return False
    finally:
        # Clean up test files
        for f in ["test_combined_output.csv", "test_combined_output.generic.csv"]:
            if os.path.exists(f):
                os.remove(f)
                print(f"🧹 Cleaned up: {f}")

def test_import_dependencies():
    """Test if all required dependencies are available."""
    print("=== Testing Dependencies ===")
    
    dependencies = {
        "pandas": True,
        "requests": True,
        "playwright": False,
        "serpapi": False,
        "crawl4ai": False,
        "pydantic": False,
        "dotenv": False
    }
    
    try:
        import pandas
        dependencies["pandas"] = True
    except ImportError:
        pass
    
    try:
        import requests
        dependencies["requests"] = True
    except ImportError:
        pass
    
    try:
        from playwright.async_api import async_playwright
        dependencies["playwright"] = True
    except ImportError:
        pass
    
    try:
        from serpapi import GoogleSearch
        dependencies["serpapi"] = True
    except ImportError:
        pass
    
    try:
        from crawl4ai import AsyncWebCrawler
        dependencies["crawl4ai"] = True
    except ImportError:
        pass
    
    try:
        from pydantic import BaseModel
        dependencies["pydantic"] = True
    except ImportError:
        pass
    
    try:
        from dotenv import load_dotenv
        dependencies["dotenv"] = True
    except ImportError:
        pass
    
    print("Dependency Status:")
    for dep, available in dependencies.items():
        status = "✅ Available" if available else "❌ Missing"
        print(f"  {dep:12s}: {status}")
    
    # Check which platforms are available
    print("\nPlatform Availability:")
    if dependencies["playwright"]:
        print("  🛒 Amazon scraping: ✅ Available")
        print("  🏪 eBay scraping: ✅ Available")
    else:
        print("  🛒 Amazon scraping: ❌ Missing playwright")
        print("  🏪 eBay scraping: ❌ Missing playwright")
    
    if dependencies["serpapi"] and dependencies["crawl4ai"] and dependencies["pydantic"]:
        print("  🔍 Google SERP scraping: ✅ Available")
    else:
        missing = []
        if not dependencies["serpapi"]: missing.append("serpapi")
        if not dependencies["crawl4ai"]: missing.append("crawl4ai")
        if not dependencies["pydantic"]: missing.append("pydantic")
        print(f"  🔍 Google SERP scraping: ❌ Missing {', '.join(missing)}")
    
    return all(dependencies[dep] for dep in ["pandas", "requests"])

def main():
    """Run all tests."""
    print("🧪 Testing Combined Scraper Functionality")
    print("=" * 50)
    
    # Test basic dependencies
    basic_deps_ok = test_import_dependencies()
    
    if not basic_deps_ok:
        print("\n❌ Basic dependencies missing. Install with:")
        print("pip install pandas requests")
        return
    
    # Test Ollama integration
    ollama_ok = test_ollama_integration()
    
    if not ollama_ok:
        print("\n❌ Ollama integration failed. Make sure:")
        print("1. Ollama is installed and running")
        print("2. llama3.2:1b model is available (run: ollama pull llama3.2:1b)")
        return
    
    # Test CSV generation
    csv_ok = test_csv_generation()
    
    if csv_ok:
        print("\n🎉 All tests passed! The combined scraper is ready to use.")
        print("\nUsage examples:")
        print("python combined_scraper.py --platforms amazon --out products.csv --headless")
        print("python combined_scraper.py --platforms amazon ebay --out products.csv --headless")
        print("python combined_scraper.py --platforms serp --query 'trending gadgets' --out serp.csv")
    else:
        print("\n❌ CSV generation test failed.")

if __name__ == "__main__":
    main()
