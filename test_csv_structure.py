#!/usr/bin/env python3
"""
Test script to show the exact CSV structure that will be generated
"""

import os
import pandas as pd
from AmazonScrapGenric import build_generic_csv_from_rows

# Sample products with some that might have '#' characters
sample_products = [
    {
        "name": "Apple iPhone 14 Pro Max (256GB) - Deep Purple",
        "category": "Electronics",
        "href": "https://amazon.com/dp/B0BDJB9NCF",
        "domain": "amazon.com",
        "source": "amazon",
        "rating": "4.5",
        "reviews": "1234"
    },
    {
        "name": "Samsung Galaxy S23 Ultra 5G (512GB) - Phantom Black",
        "category": "Electronics", 
        "href": "https://amazon.com/dp/B0BLP4JQZV",
        "domain": "amazon.com",
        "source": "amazon",
        "rating": "4.3",
        "reviews": "856"
    },
    {
        "name": "Sony WH-1000XM4 Wireless Noise Canceling Headphones - Black",
        "category": "Electronics",
        "href": "https://amazon.com/dp/B0863TXGM3",
        "domain": "amazon.com", 
        "source": "amazon",
        "rating": "4.4",
        "reviews": "2341"
    }
]

def test_csv_structure():
    print("Testing CSV structure with 3 columns: generic_name, domain, score")
    
    # Create test CSV
    test_csv = "test_structure.csv"
    
    try:
        # Test the generic name generation
        build_generic_csv_from_rows(test_csv, sample_products)
        
        # Check if generic CSV was created
        generic_csv = "test_structure.generic.csv"
        if os.path.exists(generic_csv):
            print(f"\n✅ Generic CSV created: {generic_csv}")
            
            # Read and display the CSV structure
            df = pd.read_csv(generic_csv)
            print(f"\n📊 CSV Structure:")
            print(f"Columns: {list(df.columns)}")
            print(f"Shape: {df.shape}")
            
            print(f"\n📋 Sample Data:")
            print("=" * 80)
            for idx, row in df.iterrows():
                print(f"{idx+1:2d}. Generic Name: '{row['generic_name']}'")
                print(f"    Domain: '{row['domain']}'")
                print(f"    Score: {row['score']}")
                print("-" * 60)
            
            # Show the actual CSV content
            print(f"\n📄 Raw CSV Content:")
            print("=" * 80)
            with open(generic_csv, 'r', encoding='utf-8') as f:
                content = f.read()
                print(content)
            
            return True
        else:
            print(f"❌ Generic CSV not created: {generic_csv}")
            return False
            
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        return False
    finally:
        # Clean up test files
        for f in ["test_structure.csv", "test_structure.generic.csv"]:
            if os.path.exists(f):
                os.remove(f)
                print(f"🧹 Cleaned up: {f}")

if __name__ == "__main__":
    print("=== CSV Structure Test ===")
    success = test_csv_structure()
    
    if success:
        print("\n🎉 Your CSV will have the correct structure!")
        print("Columns: generic_name, domain, score")
        print("- generic_name: Clean product name (no #, colors, or pack info)")
        print("- domain: amazon.com (or other domain)")
        print("- score: 1-3 based on frequency")
    else:
        print("\n❌ There are still issues. Check the error messages above.")
