# Combined Multi-Platform Product Scraper

A unified product scraper that combines Amazon Best Sellers, eBay Trending, and Google SERP scrapers with AI-powered generic name generation.

## Features

🛒 **Amazon Best Sellers Scraping**
- Discovers all top-level Best Sellers categories automatically
- Quality filtering (4.0+ stars, 100+ reviews)
- Multi-page scraping with auto-scrolling

🏪 **eBay Trending Scraping**
- Scrapes trending deals with dynamic loading
- Automatic "Show more" button clicking
- Deduplication by product ID

🔍 **Google SERP Product Extraction**
- Uses SerpAPI for search results
- AI-powered product extraction from web pages
- Category classification with allowed categories

🤖 **AI-Powered Generic Name Generation**
- Uses Ollama LLM for intelligent product name cleaning
- Removes colors, pack quantities, marketing text
- Keeps essential specs (storage, size, model numbers)
- Frequency-based scoring system

## Installation

### Required Dependencies
```bash
pip install pandas requests python-dotenv
```

### Optional Dependencies (by platform)

**For Amazon & eBay scraping:**
```bash
pip install playwright
playwright install chromium
```

**For Google SERP scraping:**
```bash
pip install serpapi crawl4ai pydantic
```

**For AI Generic Names:**
- Install [Ollama](https://ollama.ai/)
- Pull the model: `ollama pull llama3.2:1b`

## Configuration

### Environment Variables
Create a `.env` file:
```env
# Ollama Configuration
OLLAMA_BASE_URL=http://localhost:11434
LLM_MODEL=llama3.2:1b

# SerpAPI (for Google SERP scraping)
SERPAPI_KEY=your_serpapi_key_here
```

## Usage

### Basic Examples

**Scrape Amazon Best Sellers only:**
```bash
python combined_scraper.py --platforms amazon --out amazon_products.csv --headless
```

**Scrape multiple platforms:**
```bash
python combined_scraper.py --platforms amazon ebay --out multi_platform.csv --headless
```

**Google SERP with custom query:**
```bash
python combined_scraper.py --platforms serp --query "trending gadgets 2024" --out serp_products.csv
```

**All platforms with multiple queries:**
```bash
python combined_scraper.py --platforms amazon ebay serp --query "viral products" "trending tech" --out all_products.csv --headless
```

### Command Line Options

| Option | Description | Default |
|--------|-------------|---------|
| `--platforms` | Platforms to scrape: `amazon`, `ebay`, `serp` | `amazon` |
| `--query` | Search queries for SERP scraping | `trending products` |
| `--num` | Number of SERP results per query | `5` |
| `--out` | Output CSV filename | `combined_products.csv` |
| `--headless` | Run browsers in headless mode | `False` |
| `--no-generic` | Skip generic name generation | `False` |

## Output Files

### Raw Data CSV
Contains all scraped products with columns:
- `name`: Original product title
- `category`: Product category
- `href`: Product URL
- `domain`: Source domain (amazon.com, ebay.com, etc.)
- `source`: Source platform (amazon, ebay, serp)

### Generic Names CSV
AI-generated clean product names with columns:
- `generic_name`: Clean, searchable product name
- `domain`: Source domain
- `score`: Frequency-based score (1-3)

### Scoring System
- **Score 1**: Unique product (appears once)
- **Score 2**: Appears 2-3 times across platforms
- **Score 3**: Appears 4+ times (highly popular)

## Generic Name Examples

| Original Title | Generic Name |
|----------------|--------------|
| `Apple iPhone 14 Pro Max (256GB) - Deep Purple` | `iPhone 14 Pro Max (256GB)` |
| `1. Samsung Galaxy S23 Ultra 5G (512GB) - Phantom Black` | `Galaxy S23 Ultra 5G (512GB)` |
| `Sony WH-1000XM4 Wireless Noise Canceling Headphones - Black` | `Sony WH-1000XM4` |
| `2) Ninja Foodi Personal Blender with 18 oz BPA-Free Cup - Black/Silver (2-Pack)` | `Ninja Foodi Personal Blender` |

## Testing

Run the demo to test functionality:
```bash
python demo_combined_scraper.py
```

Run comprehensive tests:
```bash
python test_combined_scraper.py
```

## Architecture

### Core Components

1. **Ollama Integration** (`check_ollama_connection`, `generate_generic_name`)
   - LLM-powered product name cleaning
   - Concurrent processing with ThreadPoolExecutor
   - Robust error handling and fallbacks

2. **Amazon Scraper** (`scrape_amazon_bestsellers`)
   - Category discovery and navigation
   - Quality filtering by ratings and reviews
   - ASIN extraction and URL building

3. **eBay Scraper** (`scrape_ebay_trending`)
   - Dynamic content loading
   - Product ID extraction and deduplication
   - Multiple selector strategies

4. **Google SERP Scraper** (`scrape_google_serp`)
   - SerpAPI integration for search results
   - crawl4ai for content extraction
   - Category-aware product filtering

5. **CSV Generation** (`build_generic_csv_from_rows`)
   - Concurrent generic name generation
   - Frequency-based scoring
   - Clean output formatting

### Data Flow

```
Raw Product Data → Generic Name Generation → Frequency Analysis → CSV Output
     ↓                      ↓                       ↓              ↓
Platform APIs         Ollama LLM            Score Calculation   Final Files
```

## Troubleshooting

### Common Issues

**Ollama Connection Failed:**
- Ensure Ollama is running: `ollama serve`
- Check model availability: `ollama list`
- Pull required model: `ollama pull llama3.2:1b`

**Playwright Browser Issues:**
- Install browsers: `playwright install chromium`
- Use `--headless` flag for server environments

**SerpAPI Errors:**
- Verify API key in `.env` file
- Check API quota and billing

**Empty Results:**
- Check internet connection
- Verify target websites are accessible
- Review error messages in output

### Performance Tips

- Use `--headless` for faster scraping
- Limit SERP queries with `--num` parameter
- Run on machines with good internet connection
- Consider rate limiting for large-scale scraping

## License

This project is for educational and research purposes. Please respect website terms of service and implement appropriate rate limiting for production use.
