#!/usr/bin/env python3
"""
Test script to verify generic name generation is working
"""

import os
import pandas as pd
from AmazonScrapGenric import build_generic_csv_from_rows

# Sample product data (like what would come from scraping)
sample_products = [
    {
        "name": "Apple iPhone 14 Pro Max (256GB) - Deep Purple",
        "category": "Electronics",
        "href": "https://amazon.com/dp/B0BDJB9NCF",
        "domain": "amazon.com",
        "source": "amazon",
        "rating": "4.5",
        "reviews": "1234"
    },
    {
        "name": "Samsung Galaxy S23 Ultra 5G (512GB) - Phantom Black",
        "category": "Electronics", 
        "href": "https://amazon.com/dp/B0BLP4JQZV",
        "domain": "amazon.com",
        "source": "amazon",
        "rating": "4.3",
        "reviews": "856"
    },
    {
        "name": "Sony WH-1000XM4 Wireless Noise Canceling Headphones - Black",
        "category": "Electronics",
        "href": "https://amazon.com/dp/B0863TXGM3",
        "domain": "amazon.com", 
        "source": "amazon",
        "rating": "4.4",
        "reviews": "2341"
    },
    {
        "name": "Apple AirPods Pro (2nd Generation) with MagSafe Case - White",
        "category": "Electronics",
        "href": "https://amazon.com/dp/B0BDHWDR12",
        "domain": "amazon.com",
        "source": "amazon", 
        "rating": "4.6",
        "reviews": "3456"
    },
    {
        "name": "Titleist Pro V1 Golf Balls (One Dozen) - White",
        "category": "Sports",
        "href": "https://amazon.com/dp/B08XQJK123",
        "domain": "amazon.com",
        "source": "amazon",
        "rating": "4.7",
        "reviews": "789"
    }
]

def test_generic_names():
    print("Testing generic name generation with sample data...")
    
    # Create test CSV
    test_csv = "test_products.csv"
    
    try:
        # Test the generic name generation
        build_generic_csv_from_rows(test_csv, sample_products)
        
        # Check if generic CSV was created
        generic_csv = "test_products.generic.csv"
        if os.path.exists(generic_csv):
            print(f"\n✅ Generic CSV created: {generic_csv}")
            
            # Read and display results
            df = pd.read_csv(generic_csv)
            print(f"\nGenerated {len(df)} generic names:")
            print("=" * 60)
            for idx, row in df.iterrows():
                print(f"{idx+1:2d}. '{row['generic_name']}' (score: {row['score']})")
            
            # Check if we got non-empty results
            non_empty = df[df['generic_name'].str.strip() != ''].shape[0]
            empty = df[df['generic_name'].str.strip() == ''].shape[0]
            
            print(f"\n📊 Results Summary:")
            print(f"   Non-empty generic names: {non_empty}")
            print(f"   Empty generic names: {empty}")
            
            if non_empty > 0:
                print("\n✅ SUCCESS: Generic name generation is working!")
                return True
            else:
                print("\n❌ FAILURE: All generic names are empty")
                return False
        else:
            print(f"❌ Generic CSV not created: {generic_csv}")
            return False
            
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        return False
    finally:
        # Clean up test files
        for f in ["test_products.csv", "test_products.generic.csv"]:
            if os.path.exists(f):
                os.remove(f)
                print(f"🧹 Cleaned up: {f}")

if __name__ == "__main__":
    print("=== Generic Name Generation Test ===")
    success = test_generic_names()
    
    if success:
        print("\n🎉 Your scraper should now work correctly!")
        print("You can run the full scraper with:")
        print("python AmazonScrapGenric.py amazon --out products.csv --headless")
    else:
        print("\n❌ There are still issues. Check the error messages above.")
