# # # --- Generic Name Creator (brand + model only) for CSV with "Product Title" column ---
# # # Requires: pip install python-dotenv pandas requests

# # import os, time, re, requests, pandas as pd
# # from dotenv import load_dotenv

# # # ---------- config ----------
# # INPUT_CSV  = "ebay_trending_all_products.csv"   # change if needed
# # OUTPUT_CSV = "ebay_trending_all_products.generic.csv"

# # # ---------- env / ollama ----------
# # load_dotenv()
# # MODEL = os.getenv("LLM_MODEL", "llama3")
# # OLLAMA_BASE = os.getenv("OLLAMA_BASE_URL", "http://localhost:11434").rstrip("/")
# # GEN_URL = f"{OLLAMA_BASE}/api/generate"

# # # ---------- column detection ----------
# # def _norm(s: str) -> str:
# #     return re.sub(r"[^a-z0-9]", "", s.lower())

# # def _detect_title_col(cols) -> str | None:
# #     aliases = {"name", "title", "producttitle", "productname", "itemtitle"}
# #     for c in cols:
# #         if _norm(c) in aliases:
# #             return c
# #     # fallback: closest match containing "title" or "name"
# #     for c in cols:
# #         n = _norm(c)
# #         if "title" in n or "name" in n:
# #             return c
# #     return None

# # # ---------- LLM instruction ----------
# # SYSTEM_INSTRUCTION = """Extract the clean brand + model/core identifier from an e-commerce product title.
# # Rules:
# # - Keep brand and model; keep essential specs that define the model (e.g., storage GB in parentheses, screen size).
# # - Remove colors, size options, pack counts, gender/age tags, condition, seller notes, promotions, emojis.
# # - Remove trailing hyphen/pipe sections that are colors or sizes.
# # - Keep parentheses only if they contain essential specs (e.g., (256GB), model codes).
# # - Return ONLY the shortened title. No extra text.

# # Examples:
# # Apple iPhone 14 Pro Max (256GB) - Deep Purple -> Apple iPhone 14 Pro Max (256GB)
# # Nike Air Max 270 Men's Running Shoes - Black/White - Size 10 -> Nike Air Max 270
# # Samsung 55" QLED 4K Smart TV (QN55Q60DAFXZA) 2024 -> Samsung 55 QLED 4K Smart TV
# # Anker PowerCore 20000mAh Portable Charger, USB-C PD - Black -> Anker PowerCore 20000mAh Portable Charger
# # """

# # def _ollama_brand_model(raw_title: str, retries: int = 2, timeout: float = 30.0) -> str:
# #     if not raw_title or not raw_title.strip():
# #         return ""
# #     prompt = f"""{SYSTEM_INSTRUCTION}

# # Title:
# # {raw_title.strip()}

# # Clean brand+model:"""
# #     payload = {
# #         "model": MODEL,
# #         "prompt": prompt,
# #         "stream": False,
# #         "options": {"temperature": 0.1, "top_p": 0.9, "repeat_penalty": 1.05, "num_predict": 64},
# #     }
# #     for attempt in range(retries + 1):
# #         try:
# #             r = requests.post(GEN_URL, json=payload, timeout=timeout)
# #             r.raise_for_status()
# #             txt = (r.json() or {}).get("response", "").strip()
# #             return " ".join(txt.split())
# #         except Exception:
# #             if attempt >= retries:
# #                 return ""
# #             time.sleep(1.2 * (attempt + 1))
# #     return ""

# # # ---------- run ----------
# # df = pd.read_csv(INPUT_CSV)
# # col = _detect_title_col(df.columns)
# # if not col:
# #     raise ValueError(f"Could not find a title column in: {list(df.columns)}")

# # df["generic_name"] = df[col].astype(str).map(_ollama_brand_model)
# # df.to_csv(OUTPUT_CSV, index=False)
# # print(f"✅ Wrote {OUTPUT_CSV} with 'generic_name' column (source column: {col}).")
# # --- Generic Name Creator (brand + model only) ---
# # Input: ebay_trending_all_products.csv
# # Output: ebay_trending_all_products.generic.csv
# # Output file will contain ONLY one column: generic_name

# import os, time, re, requests, pandas as pd
# from dotenv import load_dotenv

# INPUT_CSV  = "ebay_trending_all_products.csv"
# OUTPUT_CSV = "ebay_trending_all_products.generic.csv"

# load_dotenv()
# MODEL = os.getenv("LLM_MODEL", "llama3")
# OLLAMA_BASE = os.getenv("OLLAMA_BASE_URL", "http://localhost:11434").rstrip("/")
# GEN_URL = f"{OLLAMA_BASE}/api/generate"

# def _norm(s: str) -> str:
#     return re.sub(r"[^a-z0-9]", "", s.lower())

# def _detect_title_col(cols) -> str | None:
#     aliases = {"name", "title", "producttitle", "productname", "itemtitle"}
#     for c in cols:
#         if _norm(c) in aliases:
#             return c
#     for c in cols:
#         n = _norm(c)
#         if "title" in n or "name" in n:
#             return c
#     return None

# SYSTEM_INSTRUCTION = """Extract a clean, search-friendly generic name from an e-commerce product title.

# Rules:
# - Always keep: Brand + Model/Core Identifier + Loft (if golf clubs) + Category + Material (if relevant).
# - Keep essential specs only (e.g., loft angles, degree, club type, iron set ranges, model numbers, storage GB, screen size).
# - Remove: colors, pack counts, flex, shaft type, length, handedness, gender tags, conditions (New, Mint, VG, Excellent, Value).
# - Keep parentheses only if they contain essential model identifiers (e.g., (256GB), model codes).
# - Output must be concise enough to paste into Google/eBay and retrieve the same product.
# - Return ONLY the cleaned title. No explanations.

# Examples:
# Apple iPhone 14 Pro Max (256GB) - Deep Purple -> Apple iPhone 14 Pro Max (256GB)
# Srixon Golf Club ZX 15* 3 Wood Stiff Project X HZRDUS Black Smoke 60 Very Good -> Srixon ZX 15° 3 Wood
# Titleist Vokey SM9 Raw F Grind 50* Gap Wedge 50-8 Dynamic Gold S200 VG -> Titleist Vokey SM9 F Grind 50° Gap Wedge
# TaylorMade Hi Toe 3 HB 60* Lob Wedge Stf 60-13 KBS 2.0 115 Excnt -> TaylorMade Hi Toe 3 60° Lob Wedge
# Samsung 55" QLED 4K Smart TV (QN55Q60DAFXZA) 2024 -> Samsung 55 QLED 4K Smart TV
# """


# def _ollama_brand_model(raw_title: str, retries: int = 2, timeout: float = 30.0) -> str:
#     if not raw_title or not raw_title.strip():
#         return ""
#     prompt = f"""{SYSTEM_INSTRUCTION}

# Title:
# {raw_title.strip()}

# Clean brand+model:"""
#     payload = {
#         "model": MODEL,
#         "prompt": prompt,
#         "stream": False,
#         "options": {"temperature": 0.1, "top_p": 0.9, "repeat_penalty": 1.05, "num_predict": 64},
#     }
#     for attempt in range(retries + 1):
#         try:
#             r = requests.post(GEN_URL, json=payload, timeout=timeout)
#             r.raise_for_status()
#             txt = (r.json() or {}).get("response", "").strip()
#             return " ".join(txt.split())
#         except Exception:
#             if attempt >= retries:
#                 return ""
#             time.sleep(1.2 * (attempt + 1))
#     return ""

# # --- Run ---
# df = pd.read_csv(INPUT_CSV)
# col = _detect_title_col(df.columns)
# if not col:
#     raise ValueError(f"Could not find a title column in: {list(df.columns)}")

# # Only keep generic names in the new file
# out_df = pd.DataFrame({
#     "generic_name": df[col].astype(str).map(_ollama_brand_model)
# })
# out_df.to_csv(OUTPUT_CSV, index=False)
# print(f"✅ Wrote {OUTPUT_CSV} with only 'generic_name' column (source column: {col}).")


# --- Generic Name Creator (search-friendly) + Frequency Score ---
# Input: ebay_trending_all_products.csv
# Output: ebay_trending_all_products.generic.csv  (columns: generic_name, score)

import os, time, re, requests, pandas as pd
from dotenv import load_dotenv

INPUT_CSV  = "ebay_trending_all_products.csv"
OUTPUT_CSV = "ebay_trending_all_products.generic.csv"

load_dotenv()
MODEL = os.getenv("LLM_MODEL", "llama3")
OLLAMA_BASE = os.getenv("OLLAMA_BASE_URL", "http://localhost:11434").rstrip("/")
GEN_URL = f"{OLLAMA_BASE}/api/generate"

def _norm(s: str) -> str:
    return re.sub(r"[^a-z0-9]", "", s.lower())

def _detect_title_col(cols) -> str | None:
    aliases = {"name", "title", "producttitle", "productname", "itemtitle"}
    for c in cols:
        if _norm(c) in aliases:
            return c
    for c in cols:
        n = _norm(c)
        if "title" in n or "name" in n:
            return c
    return None

SYSTEM_INSTRUCTION = """Extract a clean, search-friendly generic name from an e-commerce product title.

Rules:
- Always keep: Brand + Model/Core Identifier + Loft (if golf clubs) + Category + Material (if relevant).
- Keep essential specs only (e.g., loft angles, degree, club type, iron set ranges, model numbers, storage GB, screen size).
- Remove: colors, pack counts, flex, shaft type, length, handedness, gender tags, conditions (New, Mint, VG, Excellent, Value).
- Keep parentheses only if they contain essential model identifiers (e.g., (256GB), model codes).
- Output must be concise enough to paste into Google/eBay and retrieve the same product.
- Return ONLY the cleaned title. No explanations.

Examples:
Apple iPhone 14 Pro Max (256GB) - Deep Purple -> Apple iPhone 14 Pro Max (256GB)
Srixon Golf Club ZX 15* 3 Wood Stiff Project X HZRDUS Black Smoke 60 Very Good -> Srixon ZX 15° 3 Wood
Titleist Vokey SM9 Raw F Grind 50* Gap Wedge 50-8 Dynamic Gold S200 VG -> Titleist Vokey SM9 F Grind 50° Gap Wedge
TaylorMade Hi Toe 3 HB 60* Lob Wedge Stf 60-13 KBS 2.0 115 Excnt -> TaylorMade Hi Toe 3 60° Lob Wedge
Samsung 55" QLED 4K Smart TV (QN55Q60DAFXZA) 2024 -> Samsung 55 QLED 4K Smart TV
"""

def _ollama_brand_model(raw_title: str, retries: int = 2, timeout: float = 30.0) -> str:
    if not raw_title or not raw_title.strip():
        return ""
    prompt = f"""{SYSTEM_INSTRUCTION}

Title:
{raw_title.strip()}

Clean brand+model:"""
    payload = {
        "model": MODEL,
        "prompt": prompt,
        "stream": False,
        "options": {"temperature": 0.1, "top_p": 0.9, "repeat_penalty": 1.05, "num_predict": 64},
    }
    for attempt in range(retries + 1):
        try:
            r = requests.post(GEN_URL, json=payload, timeout=timeout)
            r.raise_for_status()
            txt = (r.json() or {}).get("response", "").strip()
            return " ".join(txt.split())
        except Exception:
            if attempt >= retries:
                return ""
            time.sleep(1.2 * (attempt + 1))
    return ""

def _score_from_count(cnt: int) -> int:
    # Mapping:
    # <= 3 -> 1
    # 4..8 -> 2
    # > 8  -> 3
    if cnt <= 3:
        return 1
    elif cnt <= 8:
        return 2
    else:
        return 3

# --- Run ---
df = pd.read_csv(INPUT_CSV)
col = _detect_title_col(df.columns)
if not col:
    raise ValueError(f"Could not find a title column in: {list(df.columns)}")

# 1) Build generic names
generic_series = df[col].astype(str).map(_ollama_brand_model)

# 2) Count frequencies of identical generic names
counts = generic_series.value_counts(dropna=False)

# 3) Map frequency to score
scores = generic_series.map(lambda name: 0 if (not isinstance(name, str) or not name.strip())
                            else _score_from_count(int(counts.get(name, 0))))

# 4) Save only the requested columns
out_df = pd.DataFrame({
    "generic_name": generic_series,
    "score": scores
})
out_df.to_csv(OUTPUT_CSV, index=False)
print(f"✅ Wrote {OUTPUT_CSV} with columns ['generic_name','score'] (source column: {col}).")
