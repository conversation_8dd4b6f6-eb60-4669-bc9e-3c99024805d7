#!/usr/bin/env python3
"""
Simple test script to diagnose Ollama connectivity and model issues
"""

import os
import requests
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

MODEL = os.getenv("LLM_MODEL", "llama3.2:1b")
OLLAMA_BASE = os.getenv("OLLAMA_BASE_URL", "http://localhost:11434").rstrip("/")

def test_ollama_basic():
    """Test basic Ollama connectivity"""
    print(f"Testing Ollama connection to: {OLLAMA_BASE}")
    
    try:
        # Test if Ollama is running
        response = requests.get(f"{OLLAMA_BASE}/api/tags", timeout=10)
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            models = [m.get("name", "") for m in data.get("models", [])]
            print(f"✅ Ollama is running!")
            print(f"Available models: {models}")
            
            if MODEL in models:
                print(f"✅ Model '{MODEL}' is available")
                return True
            else:
                print(f"❌ Model '{MODEL}' not found")
                print(f"To install it, run: ollama pull {MODEL}")
                return False
        else:
            print(f"❌ Ollama responded with error: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to Ollama")
        print("Make sure Ollama is running with: ollama serve")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_ollama_generation():
    """Test actual text generation"""
    if not test_ollama_basic():
        return False
        
    print(f"\nTesting text generation with model: {MODEL}")
    
    test_prompt = """Extract a clean, search-friendly generic name from an e-commerce product title.

Rules:
- Always keep: Brand + Model/Core Identifier + Category + Material (if relevant).
- Remove: colors, pack counts, conditions (New, Mint, VG, Excellent, Value).
- Return ONLY the cleaned title. No explanations.

Title:
Apple iPhone 14 Pro Max (256GB) - Deep Purple

Clean brand+model:"""

    try:
        payload = {
            "model": MODEL,
            "prompt": test_prompt,
            "stream": False,
            "options": {"temperature": 0.1, "top_p": 0.9, "repeat_penalty": 1.05, "num_predict": 64},
        }
        
        print("Sending test request...")
        response = requests.post(f"{OLLAMA_BASE}/api/generate", json=payload, timeout=60)
        
        print(f"Response Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            result = data.get("response", "").strip()
            print(f"✅ Generation successful!")
            print(f"Input: Apple iPhone 14 Pro Max (256GB) - Deep Purple")
            print(f"Output: '{result}'")
            
            if result:
                print("✅ Non-empty response received")
                return True
            else:
                print("❌ Empty response received")
                return False
        else:
            print(f"❌ Generation failed: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Generation error: {e}")
        return False

if __name__ == "__main__":
    print("=== Ollama Diagnostic Test ===")
    print(f"Model: {MODEL}")
    print(f"Base URL: {OLLAMA_BASE}")
    print()
    
    if test_ollama_generation():
        print("\n✅ All tests passed! Ollama should work with the scraper.")
    else:
        print("\n❌ Tests failed. Please fix the issues above before running the scraper.")
        print("\nCommon solutions:")
        print("1. Start Ollama: ollama serve")
        print(f"2. Install model: ollama pull {MODEL}")
        print("3. Check if Ollama is running on the correct port")
